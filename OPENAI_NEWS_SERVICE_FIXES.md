# OpenAI News Service Fixes

This document describes the fixes applied to the `OpenAINewsService` to use the correct OpenAI API format and handle the new response structure.

## Issues Fixed

### 1. **Incorrect API Call Format**
**Problem**: The original implementation used `client.chat.completions.create()` which doesn't support web search tools.

**Solution**: Changed to use `client.responses.create()` with the correct input format:

```python
# Before (incorrect)
response = await self.client.chat.completions.create(
    model="gpt-4o",
    messages=[...],
    tools=[{"type": "web_search", "web_search": {"max_results": 10}}]
)

# After (correct)
response = await self.client.responses.create(
    model="gpt-4o",
    input=[...],  # Changed from 'messages' to 'input'
    tools=[{
        "type": "web_search_preview",  # Changed tool type
        "user_location": user_location  # Added user location
    }]
)
```

### 2. **Missing User Location**
**Problem**: OpenAI web search requires user location for better search results.

**Solution**: Added user location mapping based on timezone:

```python
def _get_user_location_from_timezone(self, user_timezone: str) -> Dict[str, Any]:
    """Convert user timezone to location for OpenAI web search"""
    timezone_to_location = {
        "America/New_York": {"country": "US", "city": "New York", "region": "New York"},
        "Europe/London": {"country": "GB", "city": "London", "region": "London"},
        "Asia/Tokyo": {"country": "JP", "city": "Tokyo", "region": "Tokyo"},
        # ... more mappings
    }
    
    location = timezone_to_location.get(user_timezone)
    if location:
        return {"type": "approximate", **location}
    
    # Default to US/New York for financial news
    return {
        "type": "approximate",
        "country": "US",
        "city": "New York", 
        "region": "New York"
    }
```

### 3. **Incorrect Response Parsing**
**Problem**: The original code expected `response.choices[0].message.content` format, but OpenAI web search returns a different structure.

**Solution**: Updated to parse the new response format:

```python
# New response format handling
content = ""
sources = []

for item in response:
    if item.get("type") == "message" and item.get("status") == "completed":
        # Extract text content
        for content_item in item.get("content", []):
            if content_item.get("type") == "output_text":
                content += content_item.get("text", "")
                
                # Extract URL citations from annotations
                for annotation in content_item.get("annotations", []):
                    if annotation.get("type") == "url_citation":
                        sources.append({
                            "url": annotation.get("url", ""),
                            "title": annotation.get("title", ""),
                            "start_index": annotation.get("start_index"),
                            "end_index": annotation.get("end_index"),
                            "type": "url_citation"
                        })
    
    elif item.get("type") == "web_search_call":
        # Track web search calls
        sources.append({
            "search_call_id": item.get("id"),
            "type": "web_search_call",
            "status": item.get("status")
        })
```

## Response Format

The new OpenAI web search response format is:

```json
[
    {
        "type": "web_search_call",
        "id": "ws_67c9fa0502748190b7dd390736892e100be649c1a5ff9609",
        "status": "completed"
    },
    {
        "id": "msg_67c9fa077e288190af08fdffda2e34f20be649c1a5ff9609",
        "type": "message",
        "status": "completed",
        "role": "assistant",
        "content": [
            {
                "type": "output_text",
                "text": "On March 6, 2025, several news...",
                "annotations": [
                    {
                        "type": "url_citation",
                        "start_index": 2606,
                        "end_index": 2758,
                        "url": "https://...",
                        "title": "Title..."
                    }
                ]
            }
        ]
    }
]
```

## Methods Updated

### 1. **get_stock_news()**
- ✅ Fixed API call format
- ✅ Added user location based on timezone
- ✅ Updated response parsing
- ✅ Enhanced source tracking

### 2. **get_global_stock_macro_news()**
- ✅ Fixed API call format
- ✅ Added user location based on timezone
- ✅ Updated response parsing
- ✅ Enhanced source tracking

### 3. **web_search_stock_info()**
- ✅ Fixed API call format
- ✅ Added default US location
- ✅ Updated response parsing
- ✅ Enhanced source tracking

## Enhanced Source Tracking

The updated implementation now tracks two types of sources:

### 1. **URL Citations**
```python
{
    "url": "https://example.com/article",
    "title": "Article Title",
    "start_index": 100,
    "end_index": 200,
    "type": "url_citation"
}
```

### 2. **Web Search Calls**
```python
{
    "search_call_id": "ws_67c9fa0502748190...",
    "type": "web_search_call",
    "status": "completed"
}
```

## Location Mapping

The service now maps user timezones to appropriate locations for better search results:

| Timezone | Country | City | Region |
|----------|---------|------|--------|
| America/New_York | US | New York | New York |
| America/Los_Angeles | US | Los Angeles | California |
| Europe/London | GB | London | London |
| Asia/Tokyo | JP | Tokyo | Tokyo |
| Asia/Hong_Kong | HK | Hong Kong | Hong Kong |

**Default**: US/New York (major financial center)

## Testing Updates

Updated the test file to handle the new response format:

```python
# Show source details
url_citations = [s for s in news.get('sources', []) if s.get('type') == 'url_citation']
search_calls = [s for s in news.get('sources', []) if s.get('type') == 'web_search_call']
print(f"   📰 URL Citations: {len(url_citations)}")
print(f"   🔍 Search Calls: {len(search_calls)}")
```

## Benefits of the Fixes

### 1. **Correct API Usage**
- Uses the proper OpenAI web search API
- Follows OpenAI's documented format
- Ensures compatibility with future updates

### 2. **Better Search Results**
- Location-aware search provides more relevant results
- Financial news prioritized for major financial centers
- Timezone-based location mapping

### 3. **Enhanced Source Tracking**
- Detailed URL citations with exact positions
- Web search call tracking for transparency
- Better source attribution

### 4. **Improved Error Handling**
- Robust parsing of complex response structure
- Graceful handling of missing data
- Fallback mechanisms for edge cases

## Migration Notes

**No Breaking Changes**: The external interface remains identical. All method signatures and return formats are preserved.

**Enhanced Data**: The `sources` array now contains richer information with URL citations and search call details.

**Better Performance**: Location-aware search should provide more relevant and recent financial news.

## Testing

Run the updated test suite:

```bash
cd python_backend
python test_openai_news_service.py
```

The tests now properly validate:
- ✅ Correct API response parsing
- ✅ Source tracking functionality
- ✅ Location-based search
- ✅ Error handling

These fixes ensure the `OpenAINewsService` works correctly with OpenAI's actual web search API and provides better, more recent financial news results.
