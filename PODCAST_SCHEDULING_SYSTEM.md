# Podcast Scheduling System Documentation

This document explains the comprehensive timezone-aware podcast scheduling system implemented for LOBO AI.

## Overview

The scheduling system automatically generates personalized podcasts for all users at **6:00 AM in their local timezone**. It replaces the previous manual-only generation system and provides robust, scalable automated podcast delivery.

## Architecture

### 1. **Podcast Generation Architecture**

#### Synchronous vs Asynchronous Generation

**`_generate_podcast_sync(request, podcast_id)`**:
- **Purpose**: Generates podcast synchronously and returns result immediately
- **Used for**: Manual "Generate Now" requests and scheduled generation
- **Flow**: Fetch data → Generate script → Create audio → Save to Firebase → Return result
- **Response**: Returns `GeneratePodcastResponse` with success/error status

**`_generate_podcast_async(request, podcast_id)`**:
- **Purpose**: Wrapper for background generation
- **Used for**: Background tasks when `generateImmediately=False`
- **Flow**: Calls `_generate_podcast_sync` in background, logs errors
- **Response**: No return value (fire-and-forget)

#### Background Task System

```python
# Manual generation (immediate)
if request.generateImmediately:
    result = await _generate_podcast_sync(request, podcast_id)
    return result

# Background generation (queued)
else:
    background_tasks.add_task(_generate_podcast_async, request, podcast_id)
    return GeneratePodcastResponse(success=True, message="Started in background")
```

**FastAPI BackgroundTasks**:
- Executes tasks after response is sent to client
- Non-blocking for API response
- Suitable for fire-and-forget operations

### 2. **Scheduling System Components**

#### Core Service: `PodcastSchedulerService`

**Location**: `python_backend/services/scheduler_service.py`

**Key Features**:
- **APScheduler Integration**: Uses AsyncIOScheduler for robust job management
- **Timezone Awareness**: Schedules jobs in user's local timezone
- **Batch Processing**: Handles multiple users efficiently
- **Error Handling**: Graceful failure handling per user
- **Dynamic Rescheduling**: Updates schedules when user timezones change

#### Scheduler Configuration

```python
# APScheduler setup
jobstores = {'default': MemoryJobStore()}
executors = {
    'default': AsyncIOExecutor(),
    'threadpool': APSThreadPoolExecutor(20)
}
job_defaults = {
    'coalesce': False,      # Don't combine missed jobs
    'max_instances': 3      # Max concurrent instances per job
}
```

### 3. **Current vs New Implementation**

#### Before (Manual Only)
- **Trigger**: User clicks "Generate Now" button
- **Time**: `deliveryTime: '09:00'` (hardcoded in frontend)
- **Scope**: Single user, on-demand only
- **Timezone**: Used only for data fetching, not scheduling

#### After (Automated + Manual)
- **Trigger**: Automated at 6:00 AM local time + manual option
- **Time**: `deliveryTime: '06:00'` (configurable)
- **Scope**: All users with valid profiles
- **Timezone**: Full timezone-aware scheduling and data fetching

## Implementation Details

### 1. **User Data Fetching**

#### Firebase Integration

```python
async def get_all_users(self) -> Dict[str, Dict[str, Any]]:
    """Fetch all users with valid profiles from Firebase"""
    # Fetches from 'users' collection
    # Filters users with profile data (not just podcasts)
    # Returns {user_id: profile_data}
```

**Profile Validation**:
```python
def _is_valid_user_profile(self, user_data: Dict[str, Any]) -> bool:
    required_fields = ['selectedStocks', 'complexity', 'duration', 'tone']
    return all(field in user_data for field in required_fields) and \
           len(user_data.get('selectedStocks', [])) > 0
```

### 2. **Timezone Management**

#### Timezone Grouping

```python
async def _get_users_by_timezone(self) -> Dict[str, List[Dict[str, Any]]]:
    """Group users by timezone for efficient scheduling"""
    # Groups users: {"America/New_York": [user1, user2], "Europe/London": [user3]}
    # Validates timezones using pytz
    # Defaults invalid timezones to UTC
```

#### Timezone Validation

```python
try:
    pytz.timezone(user_timezone)
except pytz.exceptions.UnknownTimeZoneError:
    logger.warning(f"Invalid timezone '{user_timezone}', using UTC")
    user_timezone = 'UTC'
```

### 3. **Job Scheduling**

#### Cron-based Scheduling

```python
# Schedule for 6:00 AM in specific timezone
timezone = pytz.timezone(timezone_str)
trigger = CronTrigger(
    hour=6,           # 6 AM
    minute=0,         # :00 minutes
    timezone=timezone # User's local timezone
)

job = scheduler.add_job(
    func=_generate_podcasts_for_timezone,
    trigger=trigger,
    args=[timezone_str, users],
    id=f"podcast_generation_{timezone_str}",
    replace_existing=True
)
```

#### Batch Processing

```python
# Process users in batches to avoid API rate limits
batch_size = 5
for i in range(0, len(tasks), batch_size):
    batch = tasks[i:i + batch_size]
    await asyncio.gather(*batch, return_exceptions=True)
    
    # Delay between batches
    if i + batch_size < len(tasks):
        await asyncio.sleep(2)
```

### 4. **Edge Case Handling**

#### Daylight Saving Time (DST)
- **APScheduler**: Automatically handles DST transitions
- **pytz**: Provides accurate timezone calculations
- **Cron Triggers**: Adjust automatically for DST changes

#### Invalid Timezones
```python
# Fallback to UTC for invalid timezones
try:
    pytz.timezone(user_timezone)
except pytz.exceptions.UnknownTimeZoneError:
    user_timezone = 'UTC'
```

#### Missing User Data
```python
# Skip users with incomplete profiles
if not self._is_valid_user_profile(user_data):
    logger.debug(f"Skipping user {user_id}: invalid profile")
    continue
```

#### API Rate Limits
- **Batch Processing**: Limits concurrent requests
- **Delays**: 2-second delays between batches
- **Error Handling**: Individual user failures don't affect others

## API Endpoints

### 1. **Scheduler Status**

```http
GET /scheduler/status
```

**Response**:
```json
{
  "success": true,
  "scheduler_running": true,
  "jobs": [
    {
      "id": "podcast_generation_America/New_York",
      "name": "Podcast Generation - America/New_York",
      "next_run_time": "2025-01-12T06:00:00-05:00",
      "timezone": "America/New_York"
    }
  ],
  "total_jobs": 5
}
```

### 2. **Manual Rescheduling**

```http
POST /scheduler/reschedule
```

**Response**:
```json
{
  "success": true,
  "message": "All users rescheduled successfully"
}
```

## Configuration

### 1. **Environment Variables**

No additional environment variables required. Uses existing:
- `OPENAI_API_KEY`
- `ELEVENLABS_API_KEY`
- `PERPLEXITY_API_KEY`
- Firebase configuration

### 2. **Scheduling Parameters**

```python
# In PodcastSchedulerService
self.target_hour = 6      # 6 AM
self.target_minute = 0    # :00 minutes
```

**To change delivery time**:
1. Modify `target_hour` and `target_minute` in `scheduler_service.py`
2. Restart the application
3. Scheduler will automatically reschedule all users

### 3. **Dependencies**

Added to `requirements.txt`:
```
apscheduler==3.10.4
```

## Usage

### 1. **Automatic Startup**

The scheduler starts automatically when the backend starts:

```python
@app.on_event("startup")
async def startup_event():
    await scheduler_service.start_scheduler()
```

### 2. **Manual Operations**

```python
# Reschedule all users (useful after timezone changes)
await scheduler_service.schedule_all_users()

# Reschedule specific user
await scheduler_service.reschedule_user(user_id, new_timezone)

# Get job status
jobs = scheduler_service.get_scheduled_jobs()
```

### 3. **Monitoring**

```bash
# Check scheduler status
curl http://localhost:8000/scheduler/status

# Trigger manual reschedule
curl -X POST http://localhost:8000/scheduler/reschedule
```

## Best Practices

### 1. **Error Handling**
- Individual user failures don't affect others
- Comprehensive logging for debugging
- Graceful degradation for missing data

### 2. **Performance**
- Batch processing to avoid overwhelming APIs
- Efficient timezone grouping
- Memory-based job store for fast access

### 3. **Scalability**
- Concurrent execution with limits
- Timezone-based job grouping
- Configurable batch sizes

### 4. **Reliability**
- Automatic DST handling
- Job persistence across restarts
- Comprehensive error logging

## Migration from Manual System

### 1. **Backward Compatibility**
- Manual "Generate Now" still works
- Same API endpoints and responses
- No changes to frontend required

### 2. **User Experience**
- Users now receive daily podcasts automatically
- Manual generation available as backup
- Consistent 6 AM delivery in local timezone

### 3. **Monitoring**
- New scheduler status endpoints
- Enhanced logging for scheduled generation
- Job status tracking

## Troubleshooting

### 1. **Scheduler Not Starting**
```bash
# Check logs for startup errors
tail -f python_backend/loboai_backend.log | grep scheduler
```

### 2. **Jobs Not Running**
```bash
# Check scheduler status
curl http://localhost:8000/scheduler/status
```

### 3. **User Not Receiving Podcasts**
- Verify user profile completeness
- Check timezone validity
- Review individual user logs

### 4. **Performance Issues**
- Adjust batch size in `_generate_podcasts_for_timezone`
- Monitor API rate limits
- Check concurrent job limits

This scheduling system provides a robust, scalable solution for automated podcast generation while maintaining full backward compatibility with the existing manual system.
