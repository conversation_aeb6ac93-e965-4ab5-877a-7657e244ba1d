import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { auth, getUserPodcasts, getUserProfile, signOutUser } from './firebase_util';
import { useAuthState } from 'react-firebase-hooks/auth';
import { darkTheme, cssVariables } from './styles/theme';
import CombinedStockLogo from './components/CombinedStockLogo';
import { parseSRT, convertLegacyTranscript, getCurrentSubtitle } from './utils/srtParser';
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Volume2,
  LogOut,
  Calendar,
  Clock,
  FileText,
  TrendingUp,
  Search,
  Share2,
  MoreVertical,
  Headphones,
  Radio,
  Maximize2,
  Minimize2,
  Settings,
  Zap
} from 'lucide-react';

const Dashboard = () => {
  const [user, loading] = useAuthState(auth);
  const navigate = useNavigate();
  
  const [podcasts, setPodcasts] = useState([]);
  const [userProfile, setUserProfile] = useState(null);
  const [selectedPodcast, setSelectedPodcast] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showTranscript, setShowTranscript] = useState(true);
  const [highlightedText, setHighlightedText] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [volume, setVolume] = useState(1);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState('');
  const [showPlayerModal, setShowPlayerModal] = useState(false);

  const audioRef = useRef(null);

  useEffect(() => {
    if (!loading && !user) {
      navigate('/login');
    }
  }, [user, loading, navigate]);

  useEffect(() => {
    const loadData = async () => {
      if (user) {
        // Load user profile
        const profileResult = await getUserProfile(user.uid);
        if (profileResult.success && profileResult.data) {
          setUserProfile(profileResult.data);
        } else {
          // If no profile, redirect to profile setup
          navigate('/profile');
        }

        // Load podcasts
        const podcastsResult = await getUserPodcasts(user.uid);
        if (podcastsResult.success) {
          // Filter out failed podcasts - only show completed ones
          const successfulPodcasts = (podcastsResult.data || []).filter(
            podcast => podcast.status !== 'failed'
          );
          setPodcasts(successfulPodcasts);
        } else {
          // No podcasts found - set empty array to show profile view
          setPodcasts([]);
        }
      }
    };
    loadData();
  }, [user, navigate]);

  const handleSignOut = async () => {
    await signOutUser();
    navigate('/login');
  };

  const handleGenerateNow = async () => {
    if (!userProfile || !user) {
      alert('Please complete your profile setup first');
      navigate('/profile');
      return;
    }

    if (!userProfile.selectedStocks || userProfile.selectedStocks.length === 0) {
      alert('Please select at least one stock in your profile');
      navigate('/profile');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress('Initializing podcast generation...');

    try {
      const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:8000';

      setGenerationProgress('Fetching stock data...');

      const response = await fetch(`${backendUrl}/generate-podcast`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.uid,
          userName: user.displayName || "there", // Add user's name for personalization
          userProfile: {
            selectedStocks: userProfile.selectedStocks,
            complexity: userProfile.complexity,
            duration: parseInt(userProfile.duration),
            tone: userProfile.tone,
            deliveryTime: '06:00', // Fixed 6am delivery time
            timezone: userProfile.timezone || 'America/New_York'
          },
          generateImmediately: true
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      setGenerationProgress('Generating script with AI...');

      const result = await response.json();

      if (result.success) {
        setGenerationProgress('Podcast generated successfully!');

        // Refresh podcasts list
        const podcastsResult = await getUserPodcasts(user.uid);
        if (podcastsResult.success) {
          setPodcasts(podcastsResult.data);
        }

        // Show success message
        alert('🎉 Your personalized podcast has been generated successfully!');

        // Auto-select the new podcast if available
        if (result.podcastId) {
          const newPodcast = podcastsResult.data?.find(p => p.id === result.podcastId);
          if (newPodcast) {
            setSelectedPodcast(newPodcast);
          }
        }

      } else {
        throw new Error(result.error || 'Failed to generate podcast');
      }

    } catch (error) {
      console.error('Error generating podcast:', error);
      setGenerationProgress('');
      alert(`Failed to generate podcast: ${error.message}`);
    } finally {
      setIsGenerating(false);
      setTimeout(() => setGenerationProgress(''), 3000);
    }
  };

  const handlePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);

      // Update highlighted text based on current time using SRT-aware logic
      if (selectedPodcast) {
        const transcriptData = getTranscriptData(selectedPodcast);
        const currentSegment = getCurrentSubtitle(transcriptData, audioRef.current.currentTime);
        setHighlightedText(currentSegment?.text || '');
      }
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleSeek = (e) => {
    const progressBar = e.currentTarget;
    const clickX = e.nativeEvent.offsetX;
    const width = progressBar.offsetWidth;
    const newTime = (clickX / width) * duration;

    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  // Mock transcript data for demonstration (fallback only)
  const mockTranscript = [
    { start: 0, end: 5, text: "Good morning! Welcome to your personalized stock update for today." },
    { start: 5, end: 12, text: "Let's start with Apple, which closed up 2.3% yesterday at $185.42." },
    { start: 12, end: 20, text: "The company announced strong quarterly earnings, beating analyst expectations." },
    { start: 20, end: 28, text: "Tesla shares gained 1.8% following news of expanded Supercharger network." },
    { start: 28, end: 35, text: "Looking at the broader market, the S&P 500 finished the day up 0.7%." }
  ];

  // Process transcript data using SRT parser
  const getTranscriptData = (podcast) => {
    if (!podcast) return mockTranscript;

    // If we have a properly formatted transcript array (from new backend)
    if (Array.isArray(podcast.transcript)) {
      return podcast.transcript;
    }

    // If we have SRT content, parse it
    if (podcast.srtContent && typeof podcast.srtContent === 'string') {
      const parsed = parseSRT(podcast.srtContent);
      if (parsed.length > 0) {
        return parsed;
      }
    }

    // If we have legacy transcript data, convert it
    if (podcast.transcript) {
      return convertLegacyTranscript(podcast.transcript);
    }

    // Fallback to mock data
    return mockTranscript;
  };

  if (loading) {
    return (
      <>
        <style dangerouslySetInnerHTML={{ __html: cssVariables }} />
        <div
          className="min-h-screen flex items-center justify-center"
          style={{ background: darkTheme.gradients.primary }}
        >
          <div className="relative">
            <div
              className="animate-spin rounded-full h-12 w-12 border-b-2"
              style={{ borderColor: darkTheme.colors.accent.primary }}
            ></div>
            <div
              className="absolute inset-0 animate-ping rounded-full h-12 w-12 border-2 opacity-20"
              style={{ borderColor: darkTheme.colors.accent.primary }}
            ></div>
          </div>
        </div>
      </>
    );
  }

  const handlePodcastSelect = (podcast) => {
    // Stop current audio if playing
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
      setCurrentTime(0);
    }
    
    setSelectedPodcast(podcast);
    setShowPlayerModal(true);
    
    // Reset audio element to load new source
    setTimeout(() => {
      if (audioRef.current) {
        audioRef.current.load();
      }
    }, 100);
  };

  const closePlayerModal = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
    setShowPlayerModal(false);
    setSelectedPodcast(null);
    setCurrentTime(0);
    setDuration(0);
  };

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: cssVariables }} />
      <div
        className="min-h-screen"
        style={{ background: darkTheme.gradients.primary }}
      >
        <div className="container mx-auto px-4 py-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div
                  className="p-2 rounded-xl"
                  style={{
                    background: darkTheme.gradients.accent,
                    boxShadow: darkTheme.shadows.glow
                  }}
                >
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <h1
                  className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent"
                  style={{ fontFamily: darkTheme.typography.fontFamily.primary }}
                >
                  LOBO AI
                </h1>
              </div>
              {userProfile && (
                <div
                  className="text-sm hidden sm:block"
                  style={{ color: darkTheme.colors.text.secondary }}
                >
                </div>
              )}
            </div>
            <div className="flex items-center space-x-2">
              {/* Generate Now Button */}
              <button
                onClick={handleGenerateNow}
                disabled={isGenerating || !userProfile}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105 flex items-center space-x-2 ${
                  isGenerating ? 'opacity-75 cursor-not-allowed' : 'hover:shadow-lg'
                }`}
                style={{
                  background: isGenerating
                    ? darkTheme.colors.background.secondary
                    : darkTheme.gradients.button,
                  color: isGenerating
                    ? darkTheme.colors.text.secondary
                    : 'white',
                  border: `1px solid ${isGenerating
                    ? darkTheme.colors.border.secondary
                    : darkTheme.colors.accent.primary}`,
                  boxShadow: !isGenerating ? darkTheme.shadows.glow : 'none'
                }}
                title={isGenerating ? generationProgress : "Generate a new podcast now"}
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span className="text-sm">Generating...</span>
                  </>
                ) : (
                  <>
                    <Radio className="w-4 h-4" />
                    <span className="text-sm font-medium">Generate Now</span>
                  </>
                )}
              </button>

              <button
                onClick={() => navigate('/profile')}
                className="p-2 rounded-lg transition-all duration-200 hover:scale-105 group"
                style={{
                  color: darkTheme.colors.text.secondary,
                  background: darkTheme.colors.background.secondary,
                  border: `1px solid ${darkTheme.colors.border.secondary}`
                }}
                title="Edit Profile Settings"
              >
                <Settings className="w-5 h-5 group-hover:rotate-90 transition-transform duration-300" />
              </button>
              <button
                onClick={handleSignOut}
                className="p-2 rounded-lg transition-all duration-200 hover:scale-105"
                style={{
                  color: darkTheme.colors.text.secondary,
                  background: darkTheme.colors.background.secondary,
                  border: `1px solid ${darkTheme.colors.border.secondary}`
                }}
                title="Sign Out"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Main Content - Single Column */}
          <div
            className="rounded-2xl border backdrop-blur-sm"
            style={{
              background: darkTheme.gradients.card,
              borderColor: darkTheme.colors.border.primary,
              boxShadow: darkTheme.shadows.xl
            }}
          >
            <div
              className="p-6 border-b flex items-center justify-between"
              style={{ borderColor: darkTheme.colors.border.primary }}
            >
              <div>
                <h2
                  className="text-2xl font-bold"
                  style={{ color: darkTheme.colors.text.primary }}
                >
                  Your Podcasts
                </h2>
                <p
                  className="text-sm mt-1"
                  style={{ color: darkTheme.colors.text.secondary }}
                >
                  {userProfile && `${userProfile.selectedStocks?.length || 0} stocks • ${userProfile.duration} min episodes`}
                </p>
              </div>

              {/* Search */}
              <div className="relative">
                <Search
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4"
                  style={{ color: darkTheme.colors.text.tertiary }}
                />
                <input
                  type="text"
                  placeholder="Search podcasts..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 rounded-lg border transition-all duration-200 focus:outline-none"
                  style={{
                    background: darkTheme.colors.background.secondary,
                    borderColor: darkTheme.colors.border.secondary,
                    color: darkTheme.colors.text.primary,
                    width: '200px'
                  }}
                />
              </div>
            </div>

            <div className="p-6">
              {/* Generation Progress Indicator */}
              {isGenerating && (
                <div
                  className="mb-6 p-6 rounded-xl border-2 border-dashed"
                  style={{
                    background: `linear-gradient(135deg, ${darkTheme.colors.accent.primary}10 0%, ${darkTheme.colors.accent.primary}20 100%)`,
                    borderColor: darkTheme.colors.accent.primary
                  }}
                >
                  <div className="flex items-center space-x-4">
                    <div
                      className="animate-spin rounded-full h-8 w-8 border-b-2"
                      style={{ borderColor: darkTheme.colors.accent.primary }}
                    ></div>
                    <div>
                      <h3
                        className="text-lg font-bold"
                        style={{ color: darkTheme.colors.text.primary }}
                      >
                        🎙️ Generating Your Podcast
                      </h3>
                      <p
                        className="text-sm mt-1"
                        style={{ color: darkTheme.colors.text.secondary }}
                      >
                        {generationProgress}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {podcasts.length === 0 ? (
                <div className="text-center py-16">
                  <div
                    className="w-20 h-20 rounded-full mx-auto mb-6 flex items-center justify-center"
                    style={{
                      background: `${darkTheme.colors.accent.primary}20`,
                      border: `2px dashed ${darkTheme.colors.accent.primary}40`
                    }}
                  >
                    <Headphones
                      className="w-10 h-10"
                      style={{ color: darkTheme.colors.accent.primary }}
                    />
                  </div>
                  <h3
                    className="text-xl font-bold mb-3"
                    style={{ color: darkTheme.colors.text.primary }}
                  >
                    No podcasts yet
                  </h3>
                  <p
                    className="mb-6 max-w-md mx-auto"
                    style={{ color: darkTheme.colors.text.secondary }}
                  >
                    Your first personalized podcast will be generated based on your preferences.
                  </p>
                  {userProfile && (
                    <div
                      className="rounded-xl p-6 text-left max-w-md mx-auto"
                      style={{
                        background: `${darkTheme.colors.accent.primary}10`,
                        border: `1px solid ${darkTheme.colors.accent.primary}30`
                      }}
                    >
                      <h4
                        className="font-bold mb-4"
                        style={{ color: darkTheme.colors.text.primary }}
                      >
                        🎯 Your Settings:
                      </h4>
                      <div
                        className="space-y-2 text-sm"
                        style={{ color: darkTheme.colors.text.secondary }}
                      >
                        <div>📈 Stocks: {userProfile.selectedStocks?.join(', ')}</div>
                        <div>🧠 Complexity: {userProfile.complexity}</div>
                        <div>⏱️ Duration: {userProfile.duration} minutes</div>
                        <div>🎭 Tone: {userProfile.tone}</div>
                        <div>🌅 Delivery: 9:00 AM daily {userProfile.timezone ? `(${userProfile.timezone.replace('_', ' ')})` : '(timezone not set)'}</div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {podcasts
                    .filter(podcast =>
                      podcast.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      podcast.description?.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((podcast, index) => (
                    <div
                      key={podcast.id}
                      className="p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 hover:scale-[1.02]"
                      style={{
                        background: darkTheme.gradients.stockCard,
                        borderColor: darkTheme.colors.border.secondary,
                        boxShadow: darkTheme.shadows.md,
                        animationDelay: `${index * 0.1}s`
                      }}
                      onClick={() => handlePodcastSelect(podcast)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3
                            className="text-lg font-bold mb-2"
                            style={{ color: darkTheme.colors.text.primary }}
                          >
                            {podcast.title}
                          </h3>
                          <div className="flex items-center space-x-6 text-sm mb-3">
                            <div className="flex items-center space-x-2">
                              <Calendar
                                className="w-4 h-4"
                                style={{ color: darkTheme.colors.text.tertiary }}
                              />
                              <span style={{ color: darkTheme.colors.text.secondary }}>
                                {new Date(podcast.createdAt).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Clock
                                className="w-4 h-4"
                                style={{ color: darkTheme.colors.text.tertiary }}
                              />
                              <span style={{ color: darkTheme.colors.text.secondary }}>
                                {podcast.duration || '10'} min
                              </span>
                            </div>
                          </div>
                          {podcast.description && (
                            <p
                              className="text-sm line-clamp-2"
                              style={{ color: darkTheme.colors.text.tertiary }}
                            >
                              {podcast.description}
                            </p>
                          )}
                        </div>
                        <div className="ml-4">
                          <div
                            className="w-12 h-12 rounded-full flex items-center justify-center"
                            style={{
                              background: darkTheme.gradients.accent,
                              boxShadow: darkTheme.shadows.glow
                            }}
                          >
                            <Play className="w-6 h-6 text-white" fill="white" />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Player Modal */}
        {showPlayerModal && selectedPodcast && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            onClick={closePlayerModal}
          >
            <div
              className="w-full max-w-4xl max-h-[90vh] rounded-2xl border overflow-hidden"
              style={{
                background: darkTheme.gradients.card,
                borderColor: darkTheme.colors.border.primary,
                boxShadow: darkTheme.shadows.xl
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="p-6 border-b flex items-center justify-between" style={{ borderColor: darkTheme.colors.border.primary }}>
                <h2 className="text-xl font-bold" style={{ color: darkTheme.colors.text.primary }}>
                  Now Playing
                </h2>
                <button
                  onClick={closePlayerModal}
                  className="p-2 rounded-lg transition-all duration-200 hover:scale-110"
                  style={{
                    background: darkTheme.colors.background.secondary,
                    color: darkTheme.colors.text.secondary
                  }}
                >
                  ✕
                </button>
              </div>

              {/* Modal Content */}
              <div className="p-6 max-h-[calc(90vh-120px)] overflow-y-auto">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Player Section */}
                  <div className="space-y-6">
                    {/* Podcast Info */}
                    <div className="text-center">
                      <div className="mx-auto mb-4">
                        <CombinedStockLogo
                          stocks={selectedPodcast.stocks || []}
                          size={128}
                          className="mx-auto"
                        />
                      </div>
                      <h3 className="text-xl font-bold mb-2" style={{ color: darkTheme.colors.text.primary }}>
                        {selectedPodcast.title}
                      </h3>
                      <p className="text-sm mb-4" style={{ color: darkTheme.colors.text.secondary }}>
                        {selectedPodcast.description}
                      </p>
                    </div>

                    {/* Audio Element */}
                    <audio
                      ref={audioRef}
                      onTimeUpdate={handleTimeUpdate}
                      onLoadedMetadata={handleLoadedMetadata}
                      onEnded={() => setIsPlaying(false)}
                      onVolumeChange={() => setVolume(audioRef.current?.volume || 1)}
                    >
                      <source src={selectedPodcast.audioUrl} type="audio/mpeg" />
                    </audio>

                    {/* Progress Bar */}
                    <div className="space-y-2">
                      <div
                        className="w-full h-2 rounded-full cursor-pointer relative overflow-hidden"
                        style={{ background: `${darkTheme.colors.background.secondary}60` }}
                        onClick={handleSeek}
                      >
                        <div
                          className="h-full rounded-full transition-all duration-300"
                          style={{
                            background: darkTheme.gradients.accent,
                            width: `${duration ? (currentTime / duration) * 100 : 0}%`
                          }}
                        />
                      </div>
                      <div className="flex justify-between text-xs font-mono" style={{ color: darkTheme.colors.text.tertiary }}>
                        <span>{formatTime(currentTime)}</span>
                        <span>{formatTime(duration)}</span>
                      </div>
                    </div>

                    {/* Controls */}
                    <div className="flex items-center justify-center space-x-6">
                      <button
                        className="p-3 rounded-full transition-all duration-200 hover:scale-110"
                        style={{
                          background: `${darkTheme.colors.background.secondary}60`,
                          color: darkTheme.colors.text.secondary
                        }}
                        onClick={() => {
                          if (audioRef.current) {
                            audioRef.current.currentTime = Math.max(0, audioRef.current.currentTime - 15);
                          }
                        }}
                      >
                        <SkipBack className="w-5 h-5" />
                      </button>

                      <button
                        onClick={handlePlayPause}
                        className="p-4 rounded-full transition-all duration-200 hover:scale-110"
                        style={{
                          background: darkTheme.gradients.button,
                          boxShadow: darkTheme.shadows.glow
                        }}
                      >
                        {isPlaying ? (
                          <Pause className="w-6 h-6 text-white" />
                        ) : (
                          <Play className="w-6 h-6 text-white" fill="white" />
                        )}
                      </button>

                      <button
                        className="p-3 rounded-full transition-all duration-200 hover:scale-110"
                        style={{
                          background: `${darkTheme.colors.background.secondary}60`,
                          color: darkTheme.colors.text.secondary
                        }}
                        onClick={() => {
                          if (audioRef.current) {
                            audioRef.current.currentTime = Math.min(duration, audioRef.current.currentTime + 15);
                          }
                        }}
                      >
                        <SkipForward className="w-5 h-5" />
                      </button>
                    </div>

                    {/* Additional Controls */}
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Volume2 className="w-4 h-4" style={{ color: darkTheme.colors.text.tertiary }} />
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={volume}
                          onChange={(e) => {
                            const newVolume = parseFloat(e.target.value);
                            setVolume(newVolume);
                            if (audioRef.current) {
                              audioRef.current.volume = newVolume;
                            }
                          }}
                          className="flex-1"
                        />
                        <span className="text-xs font-mono w-8 text-right" style={{ color: darkTheme.colors.text.tertiary }}>
                          {Math.round(volume * 100)}%
                        </span>
                      </div>

                      <div className="flex items-center space-x-3">
                        <Zap className="w-4 h-4" style={{ color: darkTheme.colors.text.tertiary }} />
                        <select
                          value={playbackRate}
                          onChange={(e) => {
                            const rate = parseFloat(e.target.value);
                            setPlaybackRate(rate);
                            if (audioRef.current) {
                              audioRef.current.playbackRate = rate;
                            }
                          }}
                          className="flex-1 px-3 py-1.5 rounded-lg text-xs"
                          style={{
                            background: `${darkTheme.colors.background.secondary}60`,
                            color: darkTheme.colors.text.primary,
                            border: `1px solid ${darkTheme.colors.border.secondary}40`
                          }}
                        >
                          <option value="0.5">0.5x</option>
                          <option value="0.75">0.75x</option>
                          <option value="1">1x</option>
                          <option value="1.25">1.25x</option>
                          <option value="1.5">1.5x</option>
                          <option value="2">2x</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Transcript Section */}
                  {showTranscript && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="text-lg font-semibold flex items-center" style={{ color: darkTheme.colors.text.primary }}>
                          <FileText className="w-5 h-5 mr-2" />
                          Transcript
                        </h4>
                        <button
                          onClick={() => setShowTranscript(!showTranscript)}
                          className="p-2 rounded-lg transition-all duration-200"
                          style={{
                            background: `${darkTheme.colors.background.secondary}60`,
                            color: darkTheme.colors.text.secondary
                          }}
                        >
                          {showTranscript ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
                        </button>
                      </div>

                      <div className="space-y-3 max-h-96 overflow-y-auto">
                        {(() => {
                          // Use the new SRT-aware transcript processing
                          const transcriptData = getTranscriptData(selectedPodcast);

                          return transcriptData.map((segment, index) => {
                            const isHighlighted = currentTime >= segment.start && currentTime <= segment.end;
                            
                            return (
                              <div
                                key={index}
                                className={`p-4 rounded-xl cursor-pointer transition-all duration-300 hover:scale-[1.01] ${
                                  isHighlighted ? 'ring-2 ring-opacity-50' : ''
                                }`}
                                style={{
                                  background: isHighlighted
                                    ? `linear-gradient(135deg, ${darkTheme.colors.accent.primary}20 0%, ${darkTheme.colors.accent.primary}30 100%)`
                                    : `${darkTheme.colors.background.secondary}40`,
                                  ringColor: isHighlighted ? darkTheme.colors.accent.primary : 'transparent',
                                  border: `1px solid ${isHighlighted
                                    ? `${darkTheme.colors.accent.primary}60`
                                    : 'transparent'}`,
                                  boxShadow: isHighlighted ? `0 4px 20px ${darkTheme.colors.accent.primary}20` : 'none'
                                }}
                                onClick={() => {
                                  if (audioRef.current) {
                                    audioRef.current.currentTime = segment.start;
                                  }
                                }}
                              >
                                <div className="flex items-start space-x-3">
                                  <span
                                    className="text-xs font-mono px-2 py-1 rounded-md flex-shrink-0"
                                    style={{
                                      background: `${darkTheme.colors.accent.primary}20`,
                                      color: darkTheme.colors.accent.primary
                                    }}
                                  >
                                    {formatTime(segment.start)}
                                  </span>
                                  <p
                                    className="text-sm leading-relaxed"
                                    style={{ color: darkTheme.colors.text.primary }}
                                  >
                                    {segment.text}
                                  </p>
                                </div>
                              </div>
                            );
                          });
                        })()}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default Dashboard;
