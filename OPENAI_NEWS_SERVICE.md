# OpenAI News Service Implementation

This document describes the `OpenAINewsService` class that provides the same functionality as `PerplexityNewsService` but uses OpenAI's GPT-4o with web search capabilities.

## Overview

The `OpenAINewsService` is a drop-in alternative to `PerplexityNewsService` that uses OpenAI's web search tools instead of Perplexity's search API. It provides identical methods and return formats, making it easy to switch between services.

## Key Features

- **Same Interface**: Identical methods and parameters as `PerplexityNewsService`
- **GPT-4o Web Search**: Uses OpenAI's latest model with web search capabilities
- **Time-Aware Filtering**: Supports precise time range filtering for news
- **Multiple Use Cases**: Stock news, global macro news, and general web search
- **Source Tracking**: Tracks web search tool calls for transparency

## Implementation Details

### Core Technology Stack

- **Model**: `gpt-4o` (OpenAI's most capable model with web search)
- **Web Search Tool**: OpenAI's built-in web search functionality
- **API**: OpenAI Chat Completions API with tools
- **Async Support**: Full async/await implementation

### Key Differences from Perplexity

| Feature | PerplexityNewsService | OpenAINewsService |
|---------|----------------------|-------------------|
| **Model** | `sonar` / `sonar-pro` | `gpt-4o` |
| **Search Method** | Native search API | Web search tool |
| **Domain Filtering** | Built-in `search_domain_filter` | Prompt-based domain preference |
| **Recency Filtering** | Built-in `search_recency_filter` | Prompt-based time filtering |
| **Citations** | Structured citations with URLs | Tool call references |
| **Rate Limits** | Perplexity-specific limits | OpenAI API limits |

## Available Methods

### 1. Stock News Methods

```python
# Get stock news for specific time range
await openai_news_service.get_stock_news(
    symbol="AAPL",
    from_datetime=start_time,
    to_datetime=end_time,
    user_timezone="America/New_York"
)

# Get news from last 24 hours
await openai_news_service.get_stock_news_last_24h("AAPL", "America/New_York")

# Get news from previous 24 hours (24-48 hours ago)
await openai_news_service.get_stock_news_previous_24h("AAPL", "America/New_York")

# Get news for custom time range
await openai_news_service.get_stock_news_custom_range(
    symbol="AAPL",
    hours_ago_start=48,
    hours_ago_end=24,
    user_timezone="America/New_York"
)
```

### 2. Global Macro News

```python
# Get global market and macro economic news
await openai_news_service.get_global_stock_macro_news(
    from_datetime=start_time,
    to_datetime=end_time,
    user_timezone="America/New_York"
)
```

### 3. General Web Search

```python
# Perform web search for stock information
await openai_news_service.web_search_stock_info(
    query="Apple earnings Q4 2024",
    domain_filter=["bloomberg.com", "reuters.com"]
)
```

## Configuration

### Required Environment Variables

```bash
OPENAI_API_KEY=your_openai_api_key_here
```

### Optional Configuration

The service automatically handles:
- Timezone conversions
- Time range calculations
- Error handling and fallbacks
- Source tracking

## Usage Examples

### Basic Stock News

```python
from services.news_service import OpenAINewsService

# Initialize service
news_service = OpenAINewsService()

# Get recent Apple news
news = await news_service.get_stock_news_last_24h("AAPL", "America/New_York")

if 'error' not in news:
    print(f"News content: {news['news_content']}")
    print(f"Sources: {len(news['sources'])} web search results")
    print(f"Time range: {news['from_date_str']} to {news['to_date_str']}")
```

### Global Market News

```python
# Get global macro economic news
global_news = await news_service.get_global_stock_macro_news(
    user_timezone="America/New_York"
)

if 'error' not in global_news:
    print(f"Global news: {global_news['news_content']}")
    print(f"Coverage: {global_news['hours_covered']} hours")
```

### Integration with Existing Code

The service is designed as a drop-in replacement:

```python
# Before (using Perplexity)
from services.news_service import PerplexityNewsService
news_service = PerplexityNewsService()

# After (using OpenAI) - same interface!
from services.news_service import OpenAINewsService
news_service = OpenAINewsService()

# All method calls remain identical
news = await news_service.get_stock_news_last_24h("AAPL")
```

## Return Format

All methods return the same format as `PerplexityNewsService`:

```python
{
    "symbol": "AAPL",                    # For stock news
    "news_content": "...",               # Main news content
    "sources": [...],                    # Web search tool calls
    "from_datetime": "2025-01-01T...",   # ISO format
    "to_datetime": "2025-01-02T...",     # ISO format
    "from_date_str": "2025-01-01 09:00 EST",  # Human readable
    "to_date_str": "2025-01-02 09:00 EST",    # Human readable
    "user_timezone": "America/New_York",
    "hours_covered": 24,
    "timestamp": "2025-01-02T...",
    "source": "OpenAI Web Search"
}
```

## Advantages of OpenAI Implementation

### 1. **Advanced Reasoning**
- GPT-4o provides better context understanding
- More sophisticated news analysis and summarization
- Better handling of complex financial concepts

### 2. **Flexible Search**
- Web search tool can access broader range of sources
- Dynamic search strategy based on query context
- Better handling of real-time information

### 3. **Consistent API**
- Uses familiar OpenAI API patterns
- Integrated with existing OpenAI infrastructure
- Consistent rate limiting and billing

### 4. **Enhanced Processing**
- Better text processing and summarization
- More coherent news synthesis
- Superior handling of conflicting information

## Limitations

### 1. **Domain Filtering**
- No built-in domain filtering like Perplexity
- Domain preferences handled through prompts
- May be less precise than Perplexity's native filtering

### 2. **Source Citations**
- Web search tool calls instead of structured citations
- Less detailed source metadata
- URLs may not be directly accessible

### 3. **Cost Considerations**
- GPT-4o usage costs
- Web search tool usage
- Potentially higher cost per request than Perplexity

## Testing

Run the comprehensive test suite:

```bash
cd python_backend
python test_openai_news_service.py
```

**Test Coverage**:
- ✅ Stock news fetching
- ✅ Global macro news
- ✅ Web search functionality
- ✅ Service comparison with Perplexity
- ✅ Error handling
- ✅ Time zone handling

## Performance Considerations

### Response Times
- **OpenAI**: 2-8 seconds (includes web search)
- **Perplexity**: 1-4 seconds (optimized for search)

### Content Quality
- **OpenAI**: Better synthesis and analysis
- **Perplexity**: More focused on search results

### Rate Limits
- **OpenAI**: Standard API rate limits
- **Perplexity**: Search-specific limits

## Migration Guide

To switch from Perplexity to OpenAI:

1. **Update imports**:
   ```python
   # from services.news_service import PerplexityNewsService
   from services.news_service import OpenAINewsService
   ```

2. **Update initialization**:
   ```python
   # news_service = PerplexityNewsService()
   news_service = OpenAINewsService()
   ```

3. **Set environment variable**:
   ```bash
   OPENAI_API_KEY=your_openai_api_key_here
   ```

4. **No code changes required** - all methods have identical signatures

## Conclusion

The `OpenAINewsService` provides a powerful alternative to Perplexity with enhanced reasoning capabilities and flexible web search. While it may have different performance characteristics and costs, it offers the same interface and functionality, making it easy to integrate or switch between services based on your specific needs.
