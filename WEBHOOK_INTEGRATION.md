# Webhook Integration for Automatic Scheduler Updates

This document explains the webhook integration that automatically updates the podcast scheduler when user profiles change in Firebase.

## Overview

The webhook integration ensures that the podcast scheduler stays synchronized with Firebase changes without requiring manual intervention or backend restarts.

## Problem Solved

**Before**: The scheduler only read Firebase data at startup, so:
- ❌ New users weren't automatically scheduled
- ❌ Profile changes didn't update schedules
- ❌ Timezone changes weren't reflected
- ❌ Required manual reschedule or backend restart

**After**: Automatic updates via webhooks:
- ✅ New users automatically scheduled when they complete profiles
- ✅ Profile changes trigger immediate rescheduling
- ✅ Timezone changes update job schedules instantly
- ✅ Backup periodic refresh catches any missed changes

## Implementation Details

### 1. Backend Webhook Endpoint

**File**: `python_backend/main.py`

**New Endpoint**:
```python
@app.post("/scheduler/user-updated")
async def user_profile_updated(user_id: str = None):
    """
    Webhook endpoint triggered when a user profile is updated
    This ensures the scheduler stays in sync with Firebase changes
    """
    try:
        if user_id:
            logger.info(f"User profile updated webhook triggered for user: {user_id}")
            await scheduler_service.schedule_all_users()
            return {"success": True, "message": f"Scheduler updated for user {user_id}"}
        else:
            logger.info("General user profile update webhook triggered")
            await scheduler_service.schedule_all_users()
            return {"success": True, "message": "All users rescheduled successfully"}
    except Exception as e:
        logger.error(f"Error in user profile update webhook: {str(e)}")
        return {"success": False, "error": str(e)}
```

**Usage**:
```bash
# Trigger for specific user
POST /scheduler/user-updated?user_id=user123

# Trigger general reschedule
POST /scheduler/user-updated
```

### 2. Frontend Integration

**File**: `src/firebase_util.js`

**Updated `saveUserProfile` Function**:
```javascript
export const saveUserProfile = async (userId, profileData) => {
  try {
    // Save to Firebase
    await set(ref(database, `users/${userId}/profile`), {
      ...profileData,
      updatedAt: new Date().toISOString()
    });
    
    // Notify backend scheduler (NEW)
    try {
      const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:8000';
      const response = await fetch(`${backendUrl}/scheduler/user-updated?user_id=${userId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('Scheduler updated successfully:', result.message);
      }
    } catch (schedulerError) {
      // Don't fail profile save if scheduler notification fails
      console.warn('Failed to notify scheduler:', schedulerError);
    }
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
};
```

### 3. Periodic Backup Refresh

**File**: `python_backend/services/scheduler_service.py`

**Added to `start_scheduler` method**:
```python
# Schedule periodic refresh as backup (every 6 hours)
self.scheduler.add_job(
    func=self.schedule_all_users,
    trigger='interval',
    hours=6,
    id='periodic_refresh',
    name='Periodic User Refresh (Backup)',
    replace_existing=True
)
```

## How It Works

### 1. User Profile Update Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Firebase
    participant Backend
    participant Scheduler

    User->>Frontend: Updates profile
    Frontend->>Firebase: Save profile data
    Firebase-->>Frontend: Success
    Frontend->>Backend: POST /scheduler/user-updated?user_id=123
    Backend->>Scheduler: schedule_all_users()
    Scheduler->>Firebase: Fetch all users
    Scheduler->>Scheduler: Update job schedules
    Backend-->>Frontend: Success response
    Frontend-->>User: Profile saved
```

### 2. Fallback Mechanisms

1. **Webhook Failure**: If webhook fails, profile still saves successfully
2. **Periodic Refresh**: Every 6 hours, scheduler refreshes all users
3. **Manual Trigger**: `/scheduler/reschedule` endpoint for manual updates
4. **Startup Refresh**: Scheduler refreshes on backend startup

## Testing

### Run Integration Tests

```bash
cd python_backend
python test_webhook_integration.py
```

**Test Coverage**:
- ✅ Webhook endpoint functionality
- ✅ Scheduler status verification
- ✅ Frontend simulation
- ✅ Periodic refresh configuration

### Manual Testing

1. **Update a user profile** in the frontend
2. **Check backend logs** for webhook calls:
   ```bash
   tail -f loboai_backend.log | grep "webhook\|scheduler"
   ```
3. **Verify scheduler status**:
   ```bash
   curl http://localhost:8000/scheduler/status
   ```

## API Endpoints

### 1. User Profile Update Webhook

```http
POST /scheduler/user-updated?user_id={userId}
Content-Type: application/json
```

**Response**:
```json
{
  "success": true,
  "message": "Scheduler updated for user user123"
}
```

### 2. General Reschedule

```http
POST /scheduler/user-updated
```

**Response**:
```json
{
  "success": true,
  "message": "All users rescheduled successfully"
}
```

### 3. Scheduler Status

```http
GET /scheduler/status
```

**Response**:
```json
{
  "success": true,
  "scheduler_running": true,
  "jobs": [
    {
      "id": "podcast_generation_America/New_York",
      "name": "Podcast Generation - America/New_York",
      "next_run_time": "2025-01-12T06:00:00-05:00",
      "timezone": "America/New_York"
    },
    {
      "id": "periodic_refresh",
      "name": "Periodic User Refresh (Backup)",
      "next_run_time": "2025-01-12T12:00:00+00:00",
      "timezone": "UTC"
    }
  ],
  "total_jobs": 2
}
```

## Monitoring

### 1. Backend Logs

```bash
# Monitor webhook calls
tail -f loboai_backend.log | grep "webhook"

# Monitor scheduler updates
tail -f loboai_backend.log | grep "scheduler"

# Monitor user profile changes
tail -f loboai_backend.log | grep "User profile updated"
```

### 2. Frontend Console

```javascript
// Check browser console for webhook notifications
// Look for: "Scheduler updated successfully: ..."
```

### 3. Scheduler Status

```bash
# Check current jobs
curl http://localhost:8000/scheduler/status | jq

# Check if periodic refresh is scheduled
curl http://localhost:8000/scheduler/status | jq '.jobs[] | select(.name | contains("Periodic"))'
```

## Error Handling

### 1. Webhook Failures

- **Profile save continues** even if webhook fails
- **Warning logged** in frontend console
- **Periodic refresh** will catch missed updates

### 2. Backend Errors

- **Graceful error responses** with error details
- **Comprehensive logging** for debugging
- **Fallback mechanisms** ensure system reliability

### 3. Network Issues

- **Timeout handling** in frontend webhook calls
- **Retry logic** could be added if needed
- **Periodic refresh** provides backup coverage

## Benefits

### 1. Immediate Updates
- **Real-time synchronization** between Firebase and scheduler
- **No manual intervention** required
- **Instant reflection** of profile changes

### 2. Reliability
- **Multiple fallback mechanisms** ensure updates happen
- **Graceful failure handling** doesn't break user experience
- **Comprehensive logging** for troubleshooting

### 3. Scalability
- **Efficient updates** only when needed
- **Batch processing** for multiple users
- **Minimal overhead** on system resources

### 4. User Experience
- **Seamless integration** - users don't notice the complexity
- **Immediate effect** - changes take effect right away
- **Reliable delivery** - podcasts scheduled correctly

## Troubleshooting

### 1. Webhook Not Called

**Check**:
- Backend is running on correct port
- `VITE_BACKEND_URL` is set correctly in frontend
- CORS settings allow frontend domain

**Fix**:
```bash
# Verify backend URL
echo $VITE_BACKEND_URL

# Test webhook manually
curl -X POST http://localhost:8000/scheduler/user-updated
```

### 2. Scheduler Not Updating

**Check**:
- Webhook endpoint returns success
- Backend logs show scheduler activity
- Firebase has valid user profiles

**Fix**:
```bash
# Check scheduler status
curl http://localhost:8000/scheduler/status

# Trigger manual reschedule
curl -X POST http://localhost:8000/scheduler/reschedule
```

### 3. Periodic Refresh Not Working

**Check**:
- Periodic job appears in scheduler status
- Backend logs show periodic activity

**Fix**:
```bash
# Restart backend to reinitialize periodic job
python main.py
```

## Future Enhancements

### 1. Individual User Rescheduling
Currently reschedules all users for simplicity. Could optimize to reschedule only affected users.

### 2. Webhook Authentication
Add authentication to webhook endpoint for security.

### 3. Retry Logic
Add retry mechanism for failed webhook calls.

### 4. Real-time Firebase Listeners
Implement Firebase real-time listeners in backend for even more immediate updates.

This webhook integration provides a robust, scalable solution for keeping the podcast scheduler synchronized with user profile changes while maintaining excellent user experience and system reliability.
