# Global Stock Macro News Feature

This document describes the implementation of the global stock macro news section that appears at the end of each podcast.

## Overview

The LOBO AI podcast system now includes a global stock macro news section at the end of each podcast episode. This section provides broader market context after the personalized stock analysis, covering:

- Major market indices movements (S&P 500, Dow Jones, NASDAQ)
- Federal Reserve policy updates and economic indicators
- Global economic events affecting markets
- Geopolitical events impacting global markets
- Sector-wide trends and market rotations
- Currency movements and commodity prices
- Central bank actions from major economies
- Market sentiment and volatility indicators

## Implementation Details

### 1. PerplexityNewsService Enhancement

**New Method**: `get_global_stock_macro_news()`

```python
async def get_global_stock_macro_news(
    self, 
    from_datetime: Optional[datetime] = None,
    to_datetime: Optional[datetime] = None,
    user_timezone: str = "UTC"
) -> Dict[str, Any]:
```

**Features**:
- Fetches global market and macro economic news
- Time-aware filtering (defaults to last 24 hours)
- Focuses on broad market trends, not individual stocks
- Searches authoritative financial sources (Bloomberg, Reuters, CNBC, etc.)
- Includes Federal Reserve and government economic data sources

**Search Domains**:
- bloomberg.com
- reuters.com
- cnbc.com
- marketwatch.com
- finance.yahoo.com
- wsj.com
- federalreserve.gov
- bls.gov

### 2. Main Podcast Generation Integration

**File**: `python_backend/main.py`

**Changes**:
- Added `PerplexityNewsService` import and initialization
- Modified `_generate_podcast_sync()` to fetch global news
- Passes global news data to OpenAI script generation

```python
# Step 1.5: Fetch global macro news
logger.info("Fetching global macro news...")
global_news = await news_service.get_global_stock_macro_news(user_timezone=user_timezone)

# Pass to script generation
script = await openai_service.generate_dual_host_script(
    user_profile=request.userProfile,
    stock_data=stock_data,
    global_news=global_news,  # New parameter
    user_name=user_name
)
```

### 3. OpenAI Service Updates

**File**: `python_backend/services/openai_service.py`

**Method Updates**:
- `generate_dual_host_script()` - Added `global_news` parameter
- `_create_conversational_prompt()` - Enhanced to include global news section

**Prompt Structure**:
The podcast now follows this enhanced structure:
1. Opening greeting with user's name
2. Personalized stock analysis
3. Market trends and context
4. Individual stock news and events
5. **NEW: Global Stock Macro News Section**
6. Key takeaways and personalized closing

**Global News Integration**:
```python
# Format global news data for the prompt
global_news_text = ""
if global_news and 'news_content' in global_news and not global_news.get('error'):
    global_news_text = f"""
    Global Market & Macro Economic News:
    {global_news['news_content']}
    
    News Sources: {len(global_news.get('sources', []))} authoritative financial sources
    Time Range: {global_news.get('from_date_str', 'N/A')} to {global_news.get('to_date_str', 'N/A')}
    """
```

## Podcast Format Changes

### Before
```
1. Opening greeting
2. Personalized stock analysis
3. Market context
4. Stock-specific news
5. Closing remarks
```

### After
```
1. Opening greeting
2. Personalized stock analysis  
3. Market context
4. Stock-specific news
5. **Global Stock Macro News Section** ← NEW
6. Closing remarks
```

## Example Global News Content

The global news section covers topics like:

- **Market Indices**: "The S&P 500 gained 0.8% following positive economic data..."
- **Federal Reserve**: "Fed officials signaled potential rate cuts in upcoming meetings..."
- **Economic Indicators**: "July employment report showed stronger than expected job growth..."
- **Global Events**: "European markets responded positively to ECB policy announcements..."
- **Sector Trends**: "Technology sector rotation continues as investors favor AI-related stocks..."

## Testing

A comprehensive test suite has been created in `python_backend/test_global_news.py`:

```bash
cd python_backend
python test_global_news.py
```

**Test Coverage**:
- ✅ Global news service functionality
- ✅ OpenAI integration with global news
- ✅ Script generation with global content
- ✅ Error handling and fallbacks

## Configuration

**Required Environment Variables**:
```bash
PERPLEXITY_API_KEY=your_perplexity_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
```

## Benefits

1. **Comprehensive Market Coverage**: Users get both personalized and global market insights
2. **Better Context**: Personal stock performance is contextualized within broader market trends
3. **Educational Value**: Users learn about macro factors affecting their investments
4. **Professional Format**: Matches the structure of professional financial news programs
5. **Timely Information**: Global news is filtered to the same time window as personal stock news

## Backward Compatibility

- Existing podcasts continue to work unchanged
- If global news fails to load, the podcast generation continues without it
- Graceful degradation ensures service reliability
- No breaking changes to existing API endpoints

## Future Enhancements

Potential improvements could include:
- User preferences for global news topics
- Regional market focus based on user location
- Customizable global news section length
- Integration with economic calendar events
- Sector-specific global news filtering
